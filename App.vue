<script>
	import updateMinApp from '@/common/js/updateMinApp.js'
	import store from '@/store/index'
	import { getSystemInfo } from '@/utils/getLocation'
	export default {
		globalData: {
			obs:"https://xmb-new02.obs.cn-north-4.myhuaweicloud.com",
			//定位后省份信息(用于省份回填)
			cityIndex: {
				city_index: '',
				city_code: '',
				city_name: '',
			},
			//当前位置信息-由定位获取
			nowPosition: {
				province: '', //省
				city: '', //市
				district: '', //区
				adcode: '', //省份编码
				latitude: '34.223',
				longitude: '108.952',
			},
			// 屏幕信息
			systemInfo: getSystemInfo(),
		},
		onLaunch: function() {
			updateMinApp.updateMinApp()
			// uni.hideTabBar()
			//中间突起按钮
			uni.onTabBarMidButtonTap(function(e) {
				console.log('点击了中间按钮', e)
			})
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
	}
</script>

<style lang="scss">
	@import '@/common/css/underCareList.scss';

	@font-face {
		font-family: 'AlibabaPuHuiTi_3_105_Heavy';
		src: url('https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/AlibabaPuHuiTi/AlibabaPuHuiTi-3-105-Heavy.otf');
	}
	@font-face {
		font-family: 'AlibabaPuHuiTi_3_95_ExtraBold';
		src: url('https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/AlibabaPuHuiTi/AlibabaPuHuiTi-3-95-ExtraBold.otf');
	}
	page{
		width:100%;
		height:100%;
		overflow:hidden;
		background-color: #F7F8F7;
		-webkit-overflow-scrolling : touch;
		&>view{
		display: flex;
		flex-direction: column;
		width:100%;
		height:100%;
		overflow: hidden;
		}
		.main{
			flex:1;
			overflow:hidden;
			scroll-view{
				width:100%;
				height:100%;
			}
		}
  	}
	/deep/.u-form-item__message {
		padding-left: 0rpx !important;
	}
	.mt-300{
		margin-top: 300rpx;
	}
</style>