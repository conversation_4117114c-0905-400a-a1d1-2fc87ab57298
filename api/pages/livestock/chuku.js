import request from '@/common/utils/ajax'
import { nmbService, xmbtest } from '../../base'

const API_PATHS = {
  LIST: 'manage/out/page', // 出库分页列表
  ADD: 'manage/v2/livestockOutRecord', // 新增出库
}
export function chukuPage(param) {
  return request.ajax(xmbtest + API_PATHS.LIST, param, 'POST').then((res) => res.data)
}
export function chukuAdd(param) {
  return request.ajax(xmbtest + API_PATHS.ADD, param, 'POST').then((res) => res.data)
}
