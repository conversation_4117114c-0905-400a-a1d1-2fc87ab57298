import request from '@/common/utils/ajax'
import { nmbService, xmbtest } from '../../base'

const API_PATHS = {
  FEEDPAGE: 'manage/feed/page', // 饲养-列表
  FEEDADD: 'manage/feed/add', // 饲养-新增
  FEEDDETAIL: 'manage/feed/detail', // 饲养-详情
  GROWPAGE: 'manage/grow/page', // 生长检测-列表
  GROWADD: 'livestock/manage/addWeight', // 生长检测-新增
  DISEAPAGE: 'manage/immune/page', // 疾病-列表
  DISEADD: 'livestock/manage/addImmune', // 疾病-新增
  ADDBATCH: 'livestock/manage/addBatchImmuneV2',
  DAILYPAGE: 'manage/dailyState/page', // 日常记录-列表
  DAILYADD: 'manage/dailyState/add', // 日常记录-新增
  SELECTEARTAGNO: 'livestock/manage/selectByEarTagNo', // 通过耳标编号查询活畜
  ADDHEAL: 'livestock/manage/addHeal', // 治疗-新增
  HEALINGPAGE: 'manage/healing/page', // 治疗-列表
}

export function feedPage(param) {
  return request.ajax(xmbtest + API_PATHS.FEEDPAGE, param, 'POST').then((res) => res.data)
}

export function feedAdd(param) {
  return request.ajax(xmbtest + API_PATHS.FEEDADD, param, 'POST').then((res) => res.data)
}

export function feedDetail(param) {
  return request.ajax(xmbtest + API_PATHS.FEEDDETAIL, param, 'POST').then((res) => res.data)
}

export function growPage(param) {
  return request.ajax(xmbtest + API_PATHS.GROWPAGE, param, 'POST').then((res) => res.data)
}

export function growAdd(param) {
  return request.ajax(xmbtest + API_PATHS.GROWADD, param, 'POST').then((res) => res.data)
}

export function diseaPage(param) {
    return request.ajax(xmbtest + API_PATHS.DISEAPAGE, param, 'POST').then((res) => res.data)
}

export function diseaseAdd(param) {
    return request.ajax(xmbtest + API_PATHS.DISEADD, param, 'POST').then((res) => res.data)
}
export function addpatch(param) {
  return request.ajax(xmbtest + API_PATHS.ADDBATCH, param, 'POST').then((res) => res.data)
}

export function dailyPage(param) {
    return request.ajax(xmbtest + API_PATHS.DAILYPAGE, param, 'POST').then((res) => res.data)
}

export function dailyAdd(param) {
    return request.ajax(xmbtest + API_PATHS.DAILYADD, param, 'POST').then((res) => res.data)
}

export function selectEarTagNo(param) {
    return request.ajax(xmbtest + API_PATHS.SELECTEARTAGNO, param, 'POST').then((res) => res.data)
}

// 疾病（新）
export function addHeal(param) {
    return request.ajax(xmbtest + API_PATHS.ADDHEAL, param, 'POST').then((res) => res.data)
}
// 疾病列表
export function healingPage(param) {
    return request.ajax(xmbtest + API_PATHS.HEALINGPAGE, param, 'POST').then((res) => res.data)
}