import request from '@/common/utils/ajax'
import { nmbService,xmbtest } from '../base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)

const API_PATHS = {
  ADD: 'purchaseOrder/add',
  PAGE: 'purchaseOrder/page',
  INFO1: 'purchaseOrder/info',
  INFO: 'purchaseOrder/info2',
  COMPANYLIST: 'nmb/company/list',
  FLOW: 'purchaseOrder/flow',
  CONFIRM: 'purchaseGo/confirm',
  DYNAMIC: 'exportFile/transportDynamic',
  ORDEREAR:'purchaseOrder/queryEarTagsByPurchaseOrderId',
  IN:"manage/v2/updateXmbPastureRecord",
  INSTOCK:"manage/v2/listAllByUserId"
}
export function purchaseOrderAdd(param) {
	return request.ajax(nmbService + API_PATHS.ADD, param, 'POST').then((res) => res.data)
}
export function purchaseOrderPage(param) {
	return request.ajax(nmbService + API_PATHS.PAGE, param, 'POST').then((res) => res.data)
}
export function purchaseOrderInfo(param) {
	return request.ajax(nmbService + API_PATHS.INFO, param, 'POST').then((res) => res.data)
}
export function purchaseOrderInfo1(param) {
	return request.ajax(nmbService + API_PATHS.INFO1, param, 'POST').then((res) => res.data)
}
export function companyList(param) {
	return request.ajax(nmbService + API_PATHS.COMPANYLIST, param, 'POST').then((res) => res.data)
}
export function purchaseOrderFlow(param) {
	return request.ajax(nmbService + API_PATHS.FLOW, param, 'POST').then((res) => res.data)
}
export function confirm(param) {
	return request.ajax(nmbService + API_PATHS.CONFIRM, param, 'POST').then((res) => res.data)
}
export function dynamic(param) {
	return request.ajax(nmbService + API_PATHS.DYNAMIC, param, 'POST').then((res) => res.data)
}
export function orderEarTag(id) {
	return request.ajax(`${nmbService}${API_PATHS.ORDEREAR}?orderId=${id}`, null, 'GET').then((res) => res.data)
}

//入库
export function purchaseOrderIn(param) {
	return request.ajax(xmbtest + API_PATHS.IN, param, 'POST').then((res) => res.data)
}
export function orderInStockPage(param) {
	return request.ajax( `${xmbtest}${API_PATHS.INSTOCK}` , param, 'POST').then((res) => res.data)
}

