{
    "sassImplementationName" : "node-sass",
    "name" : "east-mind-ffs-applet",
    "appid" : "__UNI__A8C30EB",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "networkTimeout" : {
        "request" : 8000
    },
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {},
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_MOCK_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {},
            /* SDK配置 */
            "sdkConfigs" : {}
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx44b2523c08ba0e0d",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "minified" : true,
            "postcss" : true
        },
        "usingComponents" : true,
        /* 萤石小程序插件 */
        "plugins" : {},
        "permission" : {
            /* "scope.userLocation" : {
                "desc" : "获取您的个人及位置信息"
            },*/
            "plugins" : {},
            "scope.userFuzzyLocation" : {
                "desc" : "获取您的个人及位置信息" // 公路行驶后台定位
            }
        },
        "requiredPrivateInfos" : [ "getFuzzyLocation" ],
        "optimization" : {
            "subPackages" : true
        },
        "lazyCodeLoading" : "requiredComponents"
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "2",
    "locale" : "zh-Hans",
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "a164cb62a60b836927d1f3a2d9db3206",
                    "securityJsCode" : "",
                    "serviceHost" : ""
                }
            }
        }
    },
    "sassImplementationName": "node-sass"
}
