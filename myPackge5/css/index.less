
.main {
    min-height: calc(100vh - 126rpx);
    background-color: #F7F8F7;
    padding-bottom: 146rpx;
	overflow: auto;
}

.container {
    margin: 25rpx;
    background: #fff;
    box-sizing: border-box;
    padding: 30rpx 32rpx 40rpx;
    border-radius: 30rpx;

    /deep/ .u-form-item {
        padding: 20rpx 20rpx !important;
    }

    .tips {
        font-size: 28rpx;
        color: #999;
    }
}

.bg-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 126rpx;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
}

.add-btn {
    width: 100%;
    height: 86rpx;
    background: linear-gradient(101deg, #19AF77 0%, #40CA8F 100%);
    border-radius: 50rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 86rpx;
}

.tips {
    font-size: 28rpx;
    color: #c0c3ca;
}

.common {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    color: #333;
    margin: 10rpx;
    text-align: right;
}

.cultivate-section {
    margin: 25rpx 0;
    background: #fff;
    border-radius: 30rpx;

    .section-title {
        font-size: 26rpx;
        color: #333;
        margin-bottom: 30rpx;
        font-weight: 500;
    }

    .cultivate-options {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .cultivate-item {
            padding: 16rpx 40rpx;
            border-radius: 50rpx;
            font-size: 26rpx;
            color: #666;
            background-color: #F5F5F5;
            border: 2rpx solid transparent;
            transition: all 0.3s;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &.active {
                color: #1DB17A;
                background-color: #fff;
                border-color: #1DB17A;
            }

            &.disabled {
                cursor: not-allowed;
                opacity: 0.6;
            }
        }
    }
}

.upload-section {
    background: #fff;
    padding: 30rpx;
    border-radius: 30rpx;
    margin: 0 30rpx;

    .section-title {
        font-size: 26rpx;
        color: #333;
        margin-bottom: 10rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .upload-icon {
            width: 40rpx;
            height: 40rpx;
        }
    }

    .section-subtitle {
        font-size: 26rpx;
        color: #999;
        margin-bottom: 30rpx;
        font-weight: 400;
    }

    .uploadImage {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .itemAlready {
            width: 140rpx;
            height: 140rpx;
            border-radius: 8rpx;
            position: relative;
            margin: 0 20rpx 10rpx 0rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }

            video {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }


            .closeIcon {
                width: 32rpx;
                height: 32rpx;
                background-image: url('../../static/modalImg/error.png');
                position: absolute;
                background-size: cover;
                top: -10rpx;
                right: -10rpx;
            }

            .close-file {
                top: 5rpx;
                right: -20rpx;
            }
        }

        /* .itemAlready {
            position: relative;
            width: 140rpx;
            height: 140rpx;
            border-radius: 16rpx;
            overflow: hidden;

            image {
                width: 100%;
                height: 100%;
                border-radius: 16rpx;
            }

            .closeIcon {
                position: absolute;
                top: -10rpx;
                right: -10rpx;
                width: 32rpx;
                height: 32rpx;
                background-image: url('@/static/modalImg/error.png');
                background-size: cover;
                z-index: 10;
            }
        } */
    }
}

.section-type {
    padding: 0;
    margin: 0 0 0 15rpx;
    font-size: 14px;
    font-weight: normal !important;
}



.f-26{
	font-size: 26rpx;
}
.c-999{
	color: #999999;
}
.scroll{
	 margin-top: 50rpx;
	 padding-bottom: 140rpx;
}
.title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 24rpx;
	padding-bottom: 16rpx;
	height: 80rpx;
	line-height: 80rpx;
	position: fixed;
	top: 0rpx;
	left: 0rpx;
	width: 100%;
	background-color: #FFFFFF;
	z-index: 999;
	
}
.page-container {
  padding: 16rpx;
  background-color: #f5f5f5;
  height: calc(100% - 140rpx);  
}
.card{
	background-color: #FFFFFF;
	flex: 1;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	border-radius: 10rpx;
}
.ch{
	width: 80rpx;
}
/* 卡片容器 */
.card-wrapper {
  margin-bottom: 24rpx;
  border-radius: 12rpx;
  display: flex;
 
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background: linear-gradient( 260deg, #5ED26F 0%, #1CC271 100%);
  color: #FFFFFF;
  border-radius: 12rpx 12rpx 0 0;
}
.header-id {
  font-size: 26rpx;
  font-weight: bold;
  padding: 5rpx 0;
}
.header-status {
  font-size: 24rpx;
}

/* 卡片内容 */
.card-content {
  padding: 16rpx;
}
.info-item {
  margin-bottom: 12rpx;
  font-size: 24rpx;
  color: #333333;
}

/* 复选框样式 */
.checkbox {
  width: 40rpx;
  height: 40rpx;
  margin-top: 125rpx;
  margin-left: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.checkbox.checked {
  background-color: #4CD964;
  border-color: #4CD964;
}
.check-mark {
  font-size: 28rpx;
  color: #FFFFFF;
}
.btn-box{
	width: 100%;
	height: 130rpx;
	position: fixed;
	bottom: 0;
	left: 0;
	background-color: #FFFFFF;
	padding: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
/* 确定按钮 */
.confirm-btn {
  flex: 1;
  margin-top: 10rpx;
  padding: 20rpx;
  text-align: center;
  background: linear-gradient( 101deg, #19AF77 0%, #40CA8F 100%);
  border-radius: 50px;
  color: #FFFFFF;
  font-size: 28rpx;
}
.w-100{
	width: 150rpx;
}
.box-l {
    display: flex !important;
	align-items: center;
	justify-content: space-between; 
}
.flex-1 {
    flex: 1;
}