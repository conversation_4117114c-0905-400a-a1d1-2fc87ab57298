<template>
    <view class="common-table">
        <view class="table-header">
            <view v-for="(column, index) in columns" :key="index" class="header-cell"
                :style="{ width: column.width || 'auto', textAlign: column.align || 'left' }">
                {{ column.title }}
            </view>
        </view>

        <view class="table-body">
            <view v-for="(row, rowIndex) in data" :key="rowIndex" class="table-row"
                @click="handleRowClick(row, rowIndex)">
                <view v-for="(column, colIndex) in columns" :key="colIndex" class="body-cell"
                    :style="{ width: column.width || 'auto', textAlign: column.align || 'left' }">
                    <text>{{ row[column.key] }}</text>
                </view>
            </view>
        </view>
        <view v-if="!data || data.length === 0" class="empty-data">
            <text>暂无数据</text>
        </view>
    </view>
</template>

<script>
export default {
    name: 'CommonTable',
    props: {
        // 表格列配置
        columns: {
            type: Array,
            default: () => []
        },
        // 表格数据
        data: {
            type: Array,
            default: () => []
        },
        // 是否显示边框
        border: {
            type: Boolean,
            default: true
        },
        // 是否显示斑马纹
        stripe: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        // 行点击事件
        handleRowClick(row, index) {
            this.$emit('row-click', row, index);
        }
    }
}
</script>

<style lang="scss" scoped>
.common-table {
    width: 100%;
    background: #fff;
    border-radius: 30rpx;
    border: 1rpx solid #F7F8F7;

    .table-header {
        display: flex;
        border-bottom: 1rpx solid #E6E8EB;
        border-right: 1rpx solid #E6E8EB;

        .header-cell {
            flex: 1;
            padding: 24rpx 16rpx;
            font-size: 24rpx;
            font-weight: 500;
            color: #909399;
            border-right: 1rpx solid #E6E8EB;

            &:last-child {
                border-right: none;
            }
        }
    }

    .table-body {
        .table-row {
            display: flex;
            border-bottom: 1px solid #ebeef5;

            &:hover {
                background: #f5f7fa;
            }

            .body-cell {
                flex: 1;
                padding: 24rpx 16rpx;
                font-size: 24rpx;
                color: #333;
                border-right: 1px solid #ebeef5;
                word-break: break-all;

                &:last-child {
                    border-right: none;
                }
            }
        }
    }

    .empty-data {
        padding: 80rpx 0;
        text-align: center;
        color: #909399;
        font-size: 28rpx;
    }
}

// 斑马纹样式
.common-table.stripe {
    .table-body {
        .table-row:nth-child(even) {
            background: #fafafa;
        }
    }
}

// 无边框样式
.common-table.no-border {

    .table-header .header-cell,
    .table-body .body-cell {
        border-right: none;
    }

    .table-body .table-row {
        border-bottom: none;
    }
}
</style>
