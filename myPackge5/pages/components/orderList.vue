<template>
  <view class="page-container">
	<view class="u-p-b-20 box-l">
		 <view class="title flex-1"><text class="u-m-l-15"></text>订单列表</view>
		 <view class="w-100">
			 <u-icon name="close-circle"></u-icon>
		 </view>
	</view>
    <!-- 列表项循环 -->
	<view class="scroll" v-if="listData.length > 0">
		
		<view 
		  v-for="(item, index) in listData" 
		  :key="index" 
		  class="card-wrapper"
		  @click="choose(item)"
		>
		 <view class="ch">
			 <view class="checkbox" >
			    <u-image width="40rpx"  height="40rpx" :src="`/static/img/${item.checked?'choose.png':'nochoose.png'}`"></u-image>
				
			 </view>
		 </view>
		 <view class="card">
			  <!-- 卡片头部 -->
			  <view class="card-header">
				<text class="header-id">{{ item.purchaseOrderCode }}</text>
				<text class="header-status">已验收</text>
			  </view>

			  <!-- 卡片内容 -->
			  <view class="card-content">
				
				
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">项目经理：</view>
					<view class="flex-1 f-26">{{ item.projectManagerName== null ? "--":item.projectManagerName }}</view>
				</view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">牛源地：</view>
					<view class="flex-1 f-26">{{ item.provinceName }}</view>
				</view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">牛经纪：</view>
					<view class="flex-1 f-26">{{ item.brokerName }}</view>
				</view>
			   <view class="info-item box-l">
			   	<view class="w-100 f-26 c-999">运输司机：</view>
			   	<view class="flex-1 f-26">{{ item.driverName }}</view>
			   </view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">采购数量：</view>
					<view class="flex-1 f-26">{{ item.livestockNum }}头</view>
				</view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">品种：</view>
					<view class="flex-1 f-26">{{ item.varietiesName }}/{{ item.categoryName }}</view>
				</view>
                <view class="info-item box-l">
                	<view class="w-100 f-26 c-999">订单日期：</view>
                	<view class="flex-1 f-26">{{ item.createTime}}</view>
                </view>
				
			  </view>
		  </view>
		</view>
		
    </view>
    <view v-else class="mt-300">
			
			<u-empty text="当前还没有已验收的订单数据" mode="list"></u-empty>
	</view>
    <!-- 确定按钮 -->
	<view class="btn-box">
      <view class="confirm-btn" @click="handleConfirm">确定</view>
	</view>
  </view>
</template>

<script>
const app = getApp();
export default {
  props: {
    // 列表数据
    listData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
	  obs:app.globalData.obs,
    
    }
  },
  methods: {
    // 切换复选框状态
    toggleCheck(index) {
      this.listData[index].checked = !this.listData[index].checked
    },
    // 确定按钮事件
    handleConfirm() {
		
	  if(this.listData.length == 0){
		   this.$emit("ChooseOrder",null);
	  }
		
      const checkedItems = this.listData.find(item => item.checked)
     
	  
	  if(checkedItems == null) return uni.showToast({title:"请选择订单",icon:"none"});
	  
	  this.$emit("ChooseOrder",checkedItems);
	  
      // 这里可添加接口请求等业务逻辑
    },
	//选中元素
	choose(item){
		
		 //选中该元素后，其他元素则自动取消选择
		this.listData.forEach((i) => {
			i.checked = false;
		});
		item.checked = true;
		//强制更新组件数据
		this.$emit("UpdateListData",this.listData);
	}
  }
}
</script>


<style scoped lang="less">
 @import url('../../css/index.less');
</style>