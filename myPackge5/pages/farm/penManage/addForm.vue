<template>
  <view>
    <CustomNavbar :title="pageTitle" :titleColor="'##333333'" />
    <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
      <view class="container">
        <u-form
          :model="form"
          ref="uForm"
          :error-type="errorType"
          label-width="auto"
          :label-style="labelStyle"
        >
          <text class="u-form-item-label">所属养殖场</text>
          <u-form-item label="养殖场名称" required>
            <u-input
              v-model="pastureName"
              maxlength="10"
              placeholder="请输入养殖场名称，10个字以内"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
              disabled
            />
          </u-form-item>
        </u-form>
      </view>
      <view class="container">
        <!-- <text class="u-form-item-label">基础信息</text> -->
        <u-form
          :model="form"
          ref="uForm"
          :error-type="errorType"
          label-width="auto"
          :label-style="labelStyle"
        >
          <u-form-item label="圈舍名称" required prop="penName">
            <u-input
              v-model="form.penName"
              maxlength="10"
              placeholder="请输入圈舍名称，10个字以内"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
              :disabled="isDetail"
            />
          </u-form-item>
          <u-form-item label="圈舍编码" required prop="penCode">
            <u-input
              v-model="form.penCode"
              maxlength="8"
              placeholder="请输入圈舍编码，8位数以内"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
              :disabled="isDetail"
            />
          </u-form-item>
          <u-form-item label="圈舍面积（m²）" prop="penArea">
            <u-input
              v-model="form.penArea"
              placeholder="请输入圈舍面积"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
              :disabled="isDetail"
            />
          </u-form-item>

          <!-- 养殖数量 -->
          <u-form-item label="可容纳活畜数量（头）" prop="capacity">
            <u-input
              v-model="form.capacity"
              placeholder="请输入活畜数量"
              type="number"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
              :disabled="isDetail"
            />
          </u-form-item>
        </u-form>
      </view>
      <view class="upload-section">
        <view class="section-title">
          圈舍照片：
          <img
            v-if="!isDetail"
            class="upload-icon"
            src="../../../icon/photo.png"
            @click="uploadImage('urls', 3)"
          />
        </view>
        <view class="section-subtitle">最多不超过3张，单张图片小于2M</view>

        <view class="uploadImage" v-if="form.urls && form.urls.length">
          <view class="itemAlready" v-for="(item, index) in form.urls" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view v-if="!isDetail" class="closeIcon" @click="deleteImage(index, 'urls')"></view>
          </view>
        </view>
      </view>
      <view class="upload-section" style="margin-top: 20rpx;">
        <view class="section-title">
          <text class="u-form-item-label" style="font-size:18px">栏位</text>
          <view class="section-subtitle-btn" @click="fenceShow=true">+新增</view>
        </view>
        <view class="itemAlreadyfence" v-if="form.fenceList && form.fenceList.length">
          <view
            class="itemAlready itemAlready-fence"
            v-for="(item, index) in form.fenceList"
            :key="index"
          >
            <view class="item-fence">{{item.fenceCode}}</view>
            <view v-if="!isDetail" class="closeIcon" @click="deleteImage(index, 'fenceList')"></view>
          </view>
        </view>
      </view>
    </view>

    <view v-if="!isDetail" class="bg-box">
      <view class="add-btn" @click="submitForm">{{ isEdit ? '保存' : '确认' }}</view>
    </view>
    <u-popup
      v-model="fenceShow"
      mode="bottom"
      border-radius="14"
      @close="fenceShow=false"
      :closeable="true"
    >
      <view style="padding:20px">新增栏位</view>
      <view style="height:500rpx;padding:20px;position:relative;">
        <view class="container-item">
          <view style="line-height:45px;">栏位编码</view>
          <u-input
            v-model="fenceCode"
            placeholder="请输入8位以内的编码"
            :custom-style="customStyle"
            :placeholder-style="placeholderStyle"
          />
        </view>
        <view class="add-btn" @click="addFence">确认</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import addressPicker from '@/components/address-picker/index.vue'
import { penAdd } from '@/api/pages/livestock/farm'

import { getDicts } from '@/api/dict.js'
import { uploadFiles } from '@/api/obsUpload/index'
import CustomNavbar from '../../components/CustomNavbar.vue'

export default {
  name: 'addFarm',
  components: {
    addressPicker,
    CustomNavbar,
  },

  data() {
    return {
      systemInfo: uni.getSystemInfoSync(),
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      // 页面状态
      pastureId: '', // 养殖场ID
      pastureName: '',
      isEdit: false, // 是否编辑模式
      isDetail: false, // 是否详情模式
      fenceShow: false,
      fenceCode: '',
      form: {
        pastureId: '',
        capacity: '',
        penName: '',
        penCode: '',
        penArea: '',
        urls: '',
        fenceList: [],
      },

      errorType: ['message'],
      customStyle: { textAlign: 'right', fontSize: '26rpx' },
      labelStyle: { color: '#333', fontSize: '26rpx' },
      placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
      isSubmitting: false,
      natureList: [],
      cultivateList: [],
      rules: {
        penName: [
          {
            required: true,
            message: '请输入圈舍名称',
            trigger: ['blur', 'change'],
          },
        ],
        penCode: [
          {
            required: true,
            message: '请选择圈舍编码',
            trigger: ['blur', 'change'],
          },
        ],

        /* breedingQuantity: [{
                    required: true,
                    message: '请输入养殖数量',
                    trigger: ['blur', 'change']
                }] */
      },
    }
  },

  computed: {
    navbarTotalHeight() {
      const statusBarHeight = this.systemInfo.statusBarHeight || 0
      const navbarHeight = 44
      return statusBarHeight + navbarHeight
    },
    pageTitle() {
      // if (this.isDetail) return '养殖场详情'
      // if (this.isEdit) return '编辑养殖场'
      return '新建圈舍'
    },
    isFormValid() {
      const {
        pastureId,
        penName,
        penCode,
        // urls,
      } = this.form
      return pastureId && penCode && penName
    },
  },

  onLoad(options) {
    if (options.pastureId) {
      this.pastureId = options.pastureId
      this.form.pastureId = options.pastureId
      this.pastureName = options.pastureName
      console.log('pastureName', this.pastureName, this.pastureId)
      this.isEdit = options.mode === 'edit'
      this.isDetail = options.mode === 'detail'
    }
  },

  onReady() {
    this.$refs.uForm?.setRules?.(this.rules)
  },

  methods: {
    async loadNatureDict() {
      try {
        const res = await getDicts('pasture_nature')
        if (res && res.data) {
          this.natureList = res.data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }))
        }
      } catch (error) {
        console.error('加载字典失败:', error)
        this.$toast('加载字典失败')
      }
    },
    addFence() {
      if (!this.fenceCode) {
        this.$toast('请输入栏位编码')
        return
      }
      if (
        this.form.fenceList.some((item) => item.fenceCode === this.fenceCode)
      ) {
        this.$toast('栏位编码已存在')
        return
      }
      this.form.fenceList.push({ fenceCode: this.fenceCode })
      this.fenceCode = ''
      this.fenceShow = false
    },

    // 上传图片
    uploadImage(type, maxCount) {
      if (!this.form[type]) {
        this.form[type] = []
      }
      if (this.form[type].length >= maxCount) {
        this.$toast(`图片最多只能上传${maxCount}张`)
        return
      }
      const that = this
      uni.chooseImage({
        count: maxCount - this.form[type].length,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: function (res) {
          res.tempFilePaths.forEach((filePath) => {
            uploadFiles({
              filePath: filePath,
            })
              .then((data) => {
                that.form[type].push(data)
                that.resetField(type)
              })
              .catch((error) => {
                console.error('上传失败:', error)
                that.$toast('上传失败')
              })
          })
        },
        fail(e) {
          console.error('选择图片失败:', e)
        },
      })
    },

    // 预览图片
    previewImage(url) {
      uni.previewImage({
        urls: [url],
      })
    },

    // 删除图片
    deleteImage(index, type) {
      if (this.form[type] && this.form[type].length > index) {
        this.form[type].splice(index, 1)
        this.resetField(type)
        this.$forceUpdate()
      }
    },

    // 提交表单
    async submitForm() {
      if (this.isSubmitting) return
      if (!this.isFormValid) {
        return this.$toast('请填写完整信息')
      }

      try {
        this.isSubmitting = true
        const valid = await this.validateForm()
        if (!valid) return

        const submitData = {
          ...this.form,
          urls: this.form.urls,
        }

        const res = await penAdd(submitData)

        if (res.code === 200) {
          uni.$emit('updateFarmList')
          this.$toast(this.isEdit ? '保存成功' : '添加成功')
          uni.navigateBack({ delta: 1 })
        } else {
          throw new Error(res.message || '提交失败')
        }
      } catch (error) {
        this.handleError(error, '提交失败')
      } finally {
        this.isSubmitting = false
      }
    },

    validateForm() {
      return new Promise((resolve) => {
        this.$refs.uForm.validate((valid) => resolve(valid))
      })
    },

    handleError(error, customMessage = '') {
      console.error(error)
      this.$toast(error.message || customMessage || '操作失败')
    },

    resetField(value) {
      if (!value) return
      this.$refs.uForm?.fields?.forEach((field) => {
        if (field.prop === value) {
          field.resetField()
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
@import url('../../../css/index.less');
.u-form-item-label {
  color: #333;
  font-weight: 600;
}
.section-subtitle-btn {
  width: 90px;
  background: #e7f3ea;
  border-radius: 30px;
  font-family: SourceHanSansSC, SourceHanSansSC;
  font-weight: 400;
  font-size: 16px;
  color: #369e52;
  line-height: 35px;
  font-style: normal;
  text-align: center;
}
.itemAlready {
  position: relative;
}
.itemAlreadyfence {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.closeIcon {
  width: 34rpx;
  height: 34rpx;
  background-image: url('@/static/modalImg/error.png');
  position: absolute;
  background-size: cover;
  top: 2rpx;
  right: -10rpx;
}
.item-fence {
  width: 160px;
  height: 45px;
  // line-height: 45px;
  // width: 200px;
  background: #eee;
  padding: 10px;
  text-align: center;
  border-radius: 5px;
  margin-top: 10px;
}
.container-item {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}
.add-btn {
  width: 94%;
  position: absolute;
  bottom: 30rpx;
  // position: fixed;
  margin-top: 40px;
}
</style>
