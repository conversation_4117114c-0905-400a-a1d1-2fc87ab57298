<template>
  <view>
		<CustomNavbar  :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
    <view class="header">
      <view class="fifter">
        <img :src="`${obs}/nmb-mini/xiaoshouhetong/shaixuan.png`" alt="" @click="fifterClick" />
      </view>
    </view>

    <scroll-view class="main" scroll-y :scroll-with-animation="true" @scrolltolower="scrollToLower" refresher-enabled
      :refresher-triggered="refresherState" @refresherrefresh="bindrefresherrefresh" :scroll-top="scrollTop" >
     <!-- <contractList v-if="!isEmpty"  :list="list" /> -->
      <nullList v-if="isEmpty" />
      <view v-if="!isEmpty" :style="'height:' + (isIphonex ? 48 : 24) + 'rpx'" >
		 <view class="card ml-20 mr-20" v-for="item in list">
		 			  <!-- 卡片头部 -->
			  <view class="card-header">
				<text class="header-id">批次：{{ item.saleOrderBatchCode }}</text>
				<text class="header-status"></text>
			  </view>
 
			  <!-- 卡片内容 -->
			  <view class="card-content">
				
				
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">圈舍信息：</view>
					<view class="flex-1 f-26">{{ item.pastureName || "--" }}/{{item.penName || "--"}}</view>
				</view>
				<view class="info-item box-l" v-if="item.saleOrderId">
					<view class="w-100 f-26 c-999">订单编号：</view>
					<view class="flex-1 f-26">{{ item.saleOrderCode }}</view>
				</view>
				<view class="info-item box-l" v-if="item.saleOrderId">
					<view class="w-100 f-26 c-999">供应商：</view>
					<view class="flex-1 f-26">{{ item.saleOrderSupplierName }}</view>
				</view>
			   <view class="info-item box-l">
				<view class="w-100 f-26 c-999">入库模式：</view>
				<view class="flex-1 f-26">{{ item.earTagType == 0 ?'无耳标入库':'有耳标入库' }}</view>
			   </view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">入库数量：</view>
					<view class="flex-1 f-26">{{ item.livestockNum }}头</view>
				</view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">入库日期：</view>
					<view class="flex-1 f-26">{{ item.operateTime }}</view>
				</view>
				 
				
			  </view>
		  </view>
	  </view>
	
    </scroll-view>
	
	

    <view class="Add" @click="addIntent" v-if="$hasPermi('nmb:saleContract:add')">
      <img :src="`${obs}/nmb-mini/xiaoshouhetong/add.png`" alt="" />
    </view>
    <filterPopup @resetSearch="resetSearch" :pickerFilterShow="pickerFilterShow" @canel="pickerFilterShow = false"
      @submitForm="submitForm" />
  </view>
</template>

<script>
import filterPopup from '../components/rukuFilterPopup.vue'

import nullList from '@/components/null-list/index.vue'
import { mapState } from 'vuex'
import { getStorage } from '@/common/utils/storage.js'
import { saleContractPage } from '@/api/pages/salesContract'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
import {orderInStockPage} from '@/api/pages/purchaseOrder' 
const app = getApp();
export default {
    components: {
        CustomNavbar, filterPopup, /* contractList, */ nullList },
  data() {
    return {
	  obs : app.globalData.obs,
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      isEmpty: false,
      pickerFilterShow: false,
      filters: {},
      list: [],
      noMore: false,
      pageSize: 10,
      pageNum: 1,
      refresherState: false,
      scrollTop: 0,
    }
  },
  onLoad() {
	
	  
    this.getList();
	
    uni.$on('updateSalesContractList', () => {
      this.getList();
      console.log('updateSalesContractList')
    })    
  },
  onUnload() {
    uni.$off('updateSalesContractList');
  },
  onShow() { },
  computed: {
    ...mapState({
      userInfo: (state) => state.userDetail.user,
    }),
  },
  methods: {
	
	
    getList(val) {
      uni.showLoading({
        title: '加载中',
        icon: 'none',
      })
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
		actionType:12,
        ...val
      }
      orderInStockPage(params).then(res => {
        
		console.log(res)
		
		let pendingApproval = res.result?.list || [];
        let total = res.result.total || 0
        if (this.pageNum >= 2) {
          this.list = this.list.concat(pendingApproval)
          this.list.length >= total ? this.noMore = true : this.noMore = false;
        } else {
          if (total >= 1) {
            this.isEmpty = false;
            this.list = pendingApproval;
            this.list.length >= total ? this.noMore = true : this.noMore = false;
          } else {
            this.isEmpty = true;
          }
        } 
		uni.hideLoading()
      })
      
    },
    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList();
    },
    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      setTimeout(() => {
        this.refresherState = false
        this.getList()
        this.$toast('刷新成功')
      }, 1000)
    },

    // 搜索
    fifterClick() {
      this.pickerFilterShow = true
    },

    addIntent() {
      uni.navigateTo({
        url: `/myPackge5/pages/ruku/addForm`,
      })
    },
    resetSearch() {
      this.getList()
    },
    submitForm(val) {
      console.log(val)
      this.pickerFilterShow = false
	  val.endTime == "" ? null : val.endTime;
	  val.startTime == "" ? null : val.startTime
      this.getList(val)
    },
  },
}
</script>

<style lang="scss" scoped>

.card{
	background-color: #FFFFFF;
	flex: 1;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	border-radius: 10rpx;
	margin-left: 20rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
}
.ch{
	width: 80rpx;
}
/* 卡片容器 */
.card-wrapper {
  margin-bottom: 24rpx;
  border-radius: 12rpx;
  display: flex;
 
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background: linear-gradient( 260deg, #5ED26F 0%, #1CC271 100%);
  color: #FFFFFF;
  border-radius: 12rpx 12rpx 0 0;
}
.header-id {
  font-size: 26rpx;
  font-weight: bold;
  padding: 5rpx 0;
}
.header-status {
  font-size: 24rpx;
}

/* 卡片内容 */
.card-content {
  padding: 16rpx;
}
.info-item {
  margin-bottom: 12rpx;
  font-size: 24rpx;
  color: #333333;
}

/* 复选框样式 */
.checkbox {
  width: 40rpx;
  height: 40rpx;
  margin-top: 125rpx;
  margin-left: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.checkbox.checked {
  background-color: #4CD964;
  border-color: #4CD964;
}
.check-mark {
  font-size: 28rpx;
  color: #FFFFFF;
}
.btn-box{
	width: 100%;
	height: 130rpx;
	position: fixed;
	bottom: 0;
	left: 0;
	background-color: #FFFFFF;
	padding: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
/* 确定按钮 */
.confirm-btn {
  flex: 1;
  margin-top: 10rpx;
  padding: 20rpx;
  text-align: center;
  background: linear-gradient( 101deg, #19AF77 0%, #40CA8F 100%);
  border-radius: 50px;
  color: #FFFFFF;
  font-size: 28rpx;
}
.w-100{
	width: 150rpx;
}
.box-l {
    display: flex !important;
	align-items: center;
	justify-content: space-between; 
}
.flex-1 {
    flex: 1;
}
.header{
  width: 750rpx;
  height: 727rpx;
	display: flex;
	padding-top: 120rpx;
	/* 导航栏空间 */
	box-sizing: border-box;
	position: relative;
	background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/rukubg.png) no-repeat 100% 100%;
	background-size: 100% 100%;
  position: relative;
}
.fifter{
  position: absolute;
  top: 195rpx;
  right: 30rpx;
  img{
    width: 34rpx;
    height: 32.5rpx;
  }
}
.main{
  margin-top: -372rpx;
}
.Add{
  width: 152rpx;
  height: 145rpx;
  position: absolute;
  bottom: 290rpx;
  right:10rpx;
  img{
    width: 152rpx;
    height: 145rpx;
  }
}
</style>
