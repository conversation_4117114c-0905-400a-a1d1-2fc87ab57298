<template>
  <view class="under-care-container">
    <scroll-view
      class="under-care-list"
      scroll-y="true"
      :scroll-with-animation="true"
      @scrolltolower="scrollToLower"
      :refresher-enabled="true"
      :refresher-triggered="refresherState"
      @refresherrefresh="bindrefresherrefresh"
    >
      <view class="list-content">
        <view v-for="(item, index) in list" :key="index" class="list-item">
          <view class="item-header">
            <text class="item-time">{{ item.updateTime }}</text>
          </view>
          <view class="item-content">
            <view class="content-row">
              <text class="content-label">投喂圈舍：</text>
              <text class="content-value">{{ item.pastureName || 'xxxx' }}</text>
            </view>
            <view class="content-row">
              <text class="content-label">饲料种类：</text>
              <!-- dict-value -->
              <text class="content-value">{{ getFeedFoodText(item.feedFood) }}</text>
            </view>
            <view class="content-row">
              <text class="content-label">投喂数量：</text>
              <text class="content-value">{{ item.feedNum }}Kg</text>
            </view>
            <view class="content-row">
              <text class="content-label">投喂频次：</text>
              <text class="content-value">{{ getFeedFrequencyText(item.feedFrequency) }}</text>
            </view>
            <view class="content-row" v-if="item.manageRemark">
              <text class="content-label">备注：</text>
              <text class="content-value remark-text">{{ item.manageRemark }}</text>
            </view>
          </view>
        </view>

        <nullList v-if="isEmpty" />
        <view v-if="!noMore && list.length > 0" class="load-more">加载更多...</view>
        <view v-if="noMore && list.length > 0" class="load-more">没有更多数据了</view>
      </view>
    </scroll-view>

    <view class="fixed-add-btn" @click="addRecord">
      <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" alt="" />
    </view>
  </view>
</template>

<script>
import { feedPage } from '@/api/pages/livestock/underCare'
import { getDicts } from '@/api/dict.js'
import nullList from '@/components/null-list/index.vue'

export default {
  name: 'FeedingManagement',
  components: {
    nullList
  },
  props: {
    filterParams: {
      type: Object,
      default: () => ({})
    },
    resetSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      refresherState: false,
      noMore: false,
      isEmpty: false,
      list: [],
      pageNum: 1,
      pageSize: 10,
      // 字典数据
      feedFoodDict: {},
      feedFrequencyDict: {}
    }
  },
  mounted() {
    this.loadFeedFoodDict()
    this.loadFeedFrequencyDict()
    this.getList()

    // 监听投喂记录更新事件
    uni.$on('updateFeedList', () => {
      this.pageNum = 1
      this.noMore = false
      this.getList()
    })
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('updateFeedList')
  },
  watch: {
    filterParams: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      },
      deep: true
    },
    resetSearch: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      }
    }
  },
  methods: {
    async loadFeedFoodDict() {
      try {
        const res = await getDicts('pasture_feed_food');
        if (res && res.data) {
          const newDict = {};
          res.data.forEach(item => {
            newDict[item.dictValue] = item.dictLabel;
          });
          this.feedFoodDict = { ...this.feedFoodDict, ...newDict };
        }
      } catch (error) {
        console.error('加载饲料种类字典失败:', error);
      }
    },
    async loadFeedFrequencyDict() {
      try {
        const res = await getDicts('pasture_feed_frequency');
        if (res && res.data) {
          const newDict = {};
          res.data.forEach(item => {
            newDict[item.dictValue] = item.dictLabel;
          });
          this.feedFrequencyDict = { ...this.feedFrequencyDict, ...newDict };
        }
      } catch (error) {
        console.error('加载投喂频次字典失败:', error);
      }
    },

    getList() {
    //   uni.showLoading({ title: '加载中', icon: 'none' })

      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        startTime: this.filterParams.startTime || '',
        endTime: this.filterParams.endTime || '',
        pastureName: this.filterParams.pastureName || '',
        feedFood: this.filterParams.feedFood || ''
      }

      feedPage(params).then(res => {
        const isSuccess = res.code === 200
        const newList = isSuccess ? (res.result?.list || []) : []
        const total = isSuccess ? (res.result?.total || 0) : 0

        this.updateList(newList, total)
      }).catch(error => {
        console.error('获取饲养管理列表失败:', error)
        this.$toast('获取数据失败')
        this.updateList([], 0)
      }).finally(() => {
        uni.hideLoading()
      })
    },

    updateList(newList, total) {
      if (this.pageNum >= 2) {
        this.list = this.list.concat(newList)
        this.noMore = this.list.length >= total
      } else {
        this.isEmpty = total < 1
        this.list = total >= 1 ? newList : []
        this.noMore = this.list.length >= total
      }
    },

    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList()
    },

    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      this.getList()
      setTimeout(() => {
        this.refresherState = false
        this.$toast('刷新成功')
      }, 1000)
    },

    getFeedFoodText(value) {
      return this.feedFoodDict[value] || '未知'
    },

    getFeedFrequencyText(value) {
      return this.feedFrequencyDict[value] || '未知'
    },

      addRecord() {
          uni.navigateTo({
              url: '/myPackge5/pages/underCare/addFeedForm'
          });
      }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/underCareList.scss';

.under-care-list {
  height: calc(100vh - 500rpx) !important;
}
</style>
