<template>
  <u-popup
    v-model="showModal"
    mode="center"
    :mask="true"
    :mask-close-able="true"
    border-radius="30"
    :z-index="9999999"
    @close="handleClose"
    width="80%"
  >
    <view class="modal-container">
      <view class="close-btn" @click="handleClose">
        <u-icon name="close" color="#999" size="26"></u-icon>
      </view>

      <view class="modal-content">
        <view class="immunity-badge">
          <text class="badge-text">{{showType=='showDiseaseDetail'?'治疗':'免疫'}}</text>
        </view>

        <view class="detail-list" v-if="detailData && showType=='showVaccinationDetail'">
          <view v-for="item in detailFields" :key="item.key" class="detail-item">
            <text class="detail-label">{{ item.label }}：</text>
            <text class="detail-value">{{ getFieldValue(item.key, item.type) }}</text>
          </view>
        </view>
        <view class="detail-list" v-else-if="detailData && showType=='showDiseaseDetail'">
          <view v-for="item in disFiles" :key="item.key" class="detail-item">
            <text class="detail-label">{{ item.label }}：</text>
            <text class="detail-value">{{ getFieldValue(item.key, item.type) }}</text>
          </view>
        </view>

        <view v-else class="no-data">
          <text>暂无数据</text>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { getDicts } from "@/api/dict.js"

export default {
  name: 'VaccinationDetailModal',

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    showType: {
      type: String,
      default: 'showDiseaseDetail'
    }
  },

  data() {
    return {
      vaccinationList: []
    }
  },

  computed: {
    // 控制 u-popup 的显示状态
    showModal: {
      get() {
        return this.visible
      },
      set(value) {
        if (!value) {
          this.handleClose()
        }
      }
    },

    // 详情字段
    detailFields() {
      return [
        { key: 'earTagNo', label: '耳标编号', type: 'text' },
        // { key: 'customerName', label: '选择客户', type: 'text' },
        { key: 'operateTime', label: '免疫日期', type: 'text',},
        { key: 'vaccineName', label: '疫苗名称', type: 'text' },
        { key: 'vaccineDose', label: '剂量', type: 'text' },
        { key: 'operateType', label: '接种方式', type: 'dict' },
        { key: 'company', label: '疫苗厂家', type: 'text' },
        { key: 'vaccineBatches', label: '接种批次', type: 'text' },
        { key: 'remark', label: '免疫说明', type: 'text' },
      ]
    },
    disFiles(){
      return [
        { key: 'earTagNo', label: '耳标编号', type: 'text' },
         { key: 'operateTime', label: '治疗日期', type: 'text',},
         { key: 'diseaseSymptoms', label: '症状及并发症', type: 'text',},
         { key: 'diseaseType', label: '疾病类型', type: 'dict',},
         { key: 'diseaseReason', label: '发病原因', type: 'text',},
         { key: 'diseaseTreatmentOptions', label: '治疗方案', type: 'text',},
         { key: 'operatePeopleName', label: '兽医姓名', type: 'text',},
         { key: 'diseaseHealResult', label: '治疗结果', type: 'text',},
      ]
    }

  },

  mounted() {
    this.loadVaccinationDict()
  },

  methods: {
    async loadVaccinationDict() {
      try {
        const res = await getDicts('livestock_vaccination_way')
        if (res && res.data) {
          this.vaccinationList = res.data.map(item => ({
            label: item.dictLabel,
            value: item.dictValue
          }))
        }
      } catch (error) {
        console.error('加载接种方式字典失败:', error)
      }
    },

    /**
     * 获取字段值
     */
    getFieldValue(key, type) {
      // 添加空值检查，防止 detailData 为 null 时报错
      if (!this.detailData) {
        return '--'
      }

      const value = this.detailData[key]

      if (!value && value !== 0) {
        return '--'
      }

      if (type === 'dict' && key === 'operateType') {
        const dictItem = this.vaccinationList.find(item => item.value == value)
        return dictItem ? dictItem.label : '--'
      }

      return String(value)
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
@modal-border-radius: 20rpx;
@modal-padding: 40rpx;
@modal-max-width: 100%;
@modal-min-height: 800rpx;

@close-btn-size: 60rpx;
@close-btn-position: 15rpx;

@badge-color: #1CB179;
@badge-font-size: 42rpx;

@label-color: #999999;
@value-color: #333333;
@font-size-normal: 28rpx;
@item-margin: 25rpx;
.my-modal{
  /deep/ .u-drawer{
    background-color: indianred !important;
  }
  
}
.u-drawer-content{
  /deep/ .u-mode-center-box {
      width: 80% !important;
      max-width: 80% !important;
  }
}
.modal-container {
  width: 100%;
  max-width: @modal-max-width;
  background-image: url('https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/tanchaung.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: @modal-border-radius;
  position: relative;
  padding: 60rpx @modal-padding @modal-padding;
  min-height: @modal-min-height;
  z-index: 99999999 !important;
}

.close-btn {
  position: absolute;
  top: @close-btn-position;
  right: @close-btn-position;
  width: @close-btn-size;
  height: @close-btn-size;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  z-index: 10;
  transition: background-color 0.2s ease;

  &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.modal-content {
  width: 100%;
  height: 100%;
}

.immunity-badge {
  padding: 45rpx 30rpx 35rpx 30rpx;
  display: inline-block;

  .badge-text {
    color: @badge-color;
    font-size: @badge-font-size;
    font-weight: bold;
  }
}

.detail-list {
  border-radius: 15rpx;
  padding: 0 30rpx 50rpx 30rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: @item-margin;
  line-height: 1.4;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: @font-size-normal;
  color: @label-color;
  flex-shrink: 0;
  font-weight: 400;
}

.detail-value {
  font-size: @font-size-normal;
  color: @value-color;
  flex: 1;
  font-weight: 400;
  word-break: break-all;
  word-wrap: break-word;

  &:empty::after {
    content: '--';
    color: @label-color;
  }
}

@media screen and (max-width: 750rpx) {
  .modal-overlay {
    padding: 20rpx;
  }

  .modal-container {
    padding: 50rpx 30rpx 30rpx;
    min-height: 700rpx;
  }

  .detail-label {
    min-width: 140rpx;
    font-size: 26rpx;
  }

  .detail-value {
    font-size: 26rpx;
  }

  .immunity-badge .badge-text {
    font-size: 38rpx;
  }
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  color: @label-color;
  font-size: @font-size-normal;
}
</style>
