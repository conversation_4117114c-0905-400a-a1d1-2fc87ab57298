<template>
  <view class="login">
    <!-- <u-navbar title=" " :is-back="false"></u-navbar> -->
    <CustomNavbar title="登陆" :titleColor="'#FFFFFF'" :rightContent="true"></CustomNavbar>
    <view class="login-box">
      <view class="box">
        <img
          src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/login/login_bg.png"
          alt
          class="logo"
        />
      </view>
    </view>
    <view class="login-form">
      <u-form :model="from" ref="loginForm" :rules="rules" labelWidth="0rpx">
        <view class="form-box">
          <u-form-item label=" " prop="password" labelWidth="80" borderBottom>
            <view class="pass-box">
              <img
                src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/login/a-shouji<PERSON>shouji.png"
                alt
              />
              <u-input
                v-model="from.userName"
                type="text"
                :border="border"
                placeholder="请输入账号"
                :clearable="false"
                maxlength="20"
              />
            </view>
          </u-form-item>
          <!-- <u-form-item label=" " prop="userName" labelWidth="80" borderBottom>
            <img src="@/static/img/user_icon.png" alt="" />
            <u-input v-model="from.userName" type="text" :border="border" :clearable="false" placeholder="请输入账号" />
          </u-form-item>-->
        </view>
        <view class="form-box">
          <u-form-item label=" " prop="password" labelWidth="80" borderBottom>
            <view class="pass-box">
              <img
                src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/login/mima.png"
                alt
              />
              <u-input
                v-model="from.password"
                :type="pwdType"
                :border="border"
                :password-icon="false"
                :clearable="false"
                placeholder="请输入密码"
                maxlength="20"
              />
            </view>
          </u-form-item>
          <!-- <view class="pwd-icon" @click="changeEye">
            <img :src="`${show ? '../../../../static/img/hide_pwd.png' : '../../../../static/img/show_pwd.png'}`" alt="" />
          </view>-->
        </view>
        <u-button hover-class="none" type="primary" @click="submit">登录</u-button>
        <view class="agreement">
          <view class="agreement-icon" v-if="!checked" @click="checked=true">
            <img
              src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/login/unSelect.png"
              alt
            />
          </view>
          <view class="agreement-icon" v-else>
            <img
              src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/login/selected.png"
              alt
            />
          </view>
          <span>
            我已阅读并同意
            <text @click="showPopupFn('yonghu')">《用户服务协议》</text>
            <text @click="showPopupFn('yinsi')">《隐私权政策》</text>
          </span>
        </view>
      </u-form>
      <u-popup v-model="showPopup" height="80%" mode="bottom" border-radius="14" :closeable="true">
        <scroll-view class="popup-content" :scroll-y="true" :show-scrollbar="false">
          <view class="content">
            <rich-text :nodes="context"></rich-text>
          </view>
        </scroll-view>
      </u-popup>
    </view>
  </view>
</template>

<script>
import { loginByPsd, getPermissions, getUserRole } from '@/api/account.js'
import { mapMutations } from 'vuex'
import { setStorage, getStorage } from '@/common/utils/storage'
import { seleteProtocol } from '@/api/common.js'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
export default {
  components: {
    CustomNavbar,
  },
  name: 'loginByPsd',
  props: {
    agree: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      from: {
        /**
          平台账号  nmbadmin a123456
          牛贸易公司
          管理员  ***********
          销售员        张***********   李***********
          项目经理    张***********   李***********
          牛经纪        张***********   李***********
          品控员        张***********   李***********
          验收员        张***********   李***********
          财务            张***********    李
          客户   ***********
                ***********

          监管公司   ***********
          -----------------------------------------
          销售员        李***********
          项目经理    李***********
          牛经纪        李***********
          品控员       李***********
          司机            李12412345678
          验收员      李***********
          监管公司   ***********
          财务         张***********
         */
        userName: '',
        password: '',
      },
      show: true,
      pwdType: 'password',
      disabled: false,

      checked: false,
      showPopup: false,
      context: '',
      rules: null,
      border: null,
    }
  },

  onLoad(options) {},
  onShow() {
    this.pwdIcon = this.hideIcon
  },
  mounted() {
    this.$refs.loginForm.setRules(this.rules)
  },
  options: {
      styleIsolation: 'shared',
  },
  methods: {
    ...mapMutations('userDetail', ['SET_PERMISSIONS']),
    changeLoginType() {
      this.$emit('changeLoginType', 1)
    },
    changeEye() {
      console.log('changeEye')
      this.show = !this.show
      if (this.pwdType == 'password') {
        this.pwdType = 'text'
      } else {
        this.pwdType = 'password'
      }
    },
    showPopupFn(protocolType) {
      this.showPopup = true
      seleteProtocol({
        product: 'nmb',
        protocolType,
      }).then((res) => {
        if (res.code == 200) {
          this.context = res.result.context
        }
      })
    },
    // 提交
    submit() {
      if (!this.from.userName) return uni.$u.toast('请输入账号')
      if (!this.from.password) return uni.$u.toast('请输入密码')
      if (!this.checked) return uni.$u.toast('请先阅读并同意服务协议')
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          this.disabled = true
          uni.showLoading({
            title: '登录中...',
            mask: true,
          })
          let param = {
            username: this.from.userName,
            password: this.from.password,
          }
          let login = await loginByPsd(param)
          this.disabled = false
          setTimeout(() => {
            uni.hideLoading()
          }, 1500)
          if (login.code == 200) {
            console.log(login)
            uni.switchTab({
              url: '/pages/index/index',
            })
            setTimeout(() => {}, 1000)
            this.$store.commit('user/SET_TOKEN', login.result.token)
            this.$store.commit(
              'user/SET_TENANTID',
              login.result.tradingCompanyList[0]?.tenantId
            )
            this.$store.commit('user/SET_USER', login.result)
            setStorage('tradingCompanyList', login.result.tradingCompanyList)
            // setStorage('permissions', ['*:*:*'])
            setStorage('permissions', login.result.permissions)
          } else {
            this.disabled = false
            uni.$u.toast(login.message)
          }
        } else {
          console.log('验证失败')
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
/deep/ .u-border-bottom:after {
  display: none;
}
/deep/ .u-form-item--right__content__slot {
  display: flex !important;
  align-items: center;
}
/deep/ .u-form-item__body {
  display: block;
}

.login {
  width: 100%;
  height: 1500rpx;
  .login-box {
    width: 100%;
    height: 483rpx;
    img {
      width: 100%;
      height: 483rpx;
    }
  }
  .login-form {
    width: 100%;
    height: 1170rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    backdrop-filter: blur(14px);
    padding: 106rpx 50rpx;
    margin-top: -30rpx;
    .form-box {
      padding-left: 58rpx;
      background: #f5f5f5;
      margin-bottom: 32rpx;
      width: 650rpx;
      height: 110rpx;
      background: #f5f5f5;
      border-radius: 59rpx;
      position: relative;

      /deep/ .u-input__input {
        width: 450rpx;
      }
      .pass-box {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      img {
        width: 34rpx;
        height: 38rpx;
        margin-right: 36rpx;
      }
      .pwd-icon {
        position: absolute;
        right: 20rpx;
        width: 35rpx;
        height: 35rpx;
      }
    }
    /deep/ .u-btn {
      width: 650rpx;
      height: 100rpx;
      background: linear-gradient(101deg, #19af77 0%, #40ca8f 100%);
      border-radius: 50rpx;
      margin-top: 81rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 36rpx;
      color: #ffffff;
    }
  }
}
.agreement {
  display: flex;
  font-size: 24rpx;
  margin-top: 40rpx;
  color: #999999;
  .agreement-icon {
    margin-right: 8rpx;
    img {
      width: 30rpx;
      height: 30rpx;
    }
  }
  text {
    color: #25c160;
  }
}
.content {
  height: 80%;
  overflow: scroll;
  padding: 40rpx 30rpx;
}
</style>
